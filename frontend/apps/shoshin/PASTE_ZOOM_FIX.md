# Paste Zoom Fix

## Problem
When nodes were pasted in the Shoshin editor, the paste position was inconsistent depending on the zoom level. Nodes would appear far from the cursor when zoomed in or out, instead of always pasting at the cursor location.

## Root Cause
The issue was that the cursor position tracking and paste operation weren't using consistent coordinate transformations. The cursor position was being stored in flow coordinates, but when the zoom level changed, the relationship between screen coordinates and flow coordinates changed, causing the paste position to be incorrect.

## Solution
The fix involves several improvements to ensure consistent paste positioning regardless of zoom level:

### 1. Enhanced Cursor Position Tracking
- Added global mouse position tracking that stores raw screen coordinates
- Enhanced the cursor position tracking to use the same coordinate transformation as the drop functionality
- Added a global mouse move listener to track cursor position even when not directly over the canvas

### 2. Improved Paste Logic
- Modified the paste function to dynamically get the current mouse position at the time of paste
- Added fallback logic that tries multiple methods to determine the correct paste position:
  1. Use explicitly provided position (if any)
  2. Get current mouse position and transform it using ReactFlow's `screenToFlowPosition`
  3. Use viewport center as a fallback
  4. Use last stored cursor position as final fallback

### 3. Global ReactFlow Instance Access
- Store the ReactFlow instance globally so it can be accessed from the store
- This allows the paste function to use the same coordinate transformation methods that work correctly for drag-and-drop

## Key Changes

### MainCanvas.tsx
- Added global storage of ReactFlow instance: `window.__reactFlowInstance`
- Added global mouse position tracking: `window.__lastMousePosition`
- Enhanced mouse move handlers to use consistent coordinate transformation

### editorStore.ts
- Improved paste function to dynamically calculate paste position using current mouse coordinates
- Added multiple fallback strategies for determining paste position
- Ensured all coordinate transformations use ReactFlow's `screenToFlowPosition` method

## Testing
A test script (`test-paste-zoom.js`) is provided to verify the fix works correctly at different zoom levels. The test:
1. Sets different zoom levels (0.5x, 1x, 1.5x, 2x)
2. Creates and copies test nodes
3. Simulates paste operations
4. Verifies that nodes are pasted close to the expected position

## Usage
The fix is automatic and requires no changes to user workflow. Users can now:
- Copy nodes with Ctrl+C
- Paste nodes with Ctrl+V at any zoom level
- Expect nodes to always paste at or near the cursor position
- Use the same paste functionality regardless of zoom level

## Technical Details
The key insight is that ReactFlow's `screenToFlowPosition` method correctly handles the coordinate transformation from screen coordinates to flow coordinates, accounting for zoom level, pan position, and other viewport transformations. By consistently using this method for both cursor tracking and paste positioning, we ensure that the paste operation works correctly at any zoom level.
